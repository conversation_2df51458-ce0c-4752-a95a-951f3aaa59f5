# API Documentation

## Overview

This API provides a comprehensive set of endpoints for managing a collaborative work platform with features including user management, organizations, departments, tasks, chat functionality, notifications, and feedback systems.

**Base URL:** `/api/v1/`

**Authentication:** All endpoints require JWT authentication via <PERSON><PERSON> token in the Authorization header.

## Authentication

### Headers
```
Authorization: Bearer <JWT_TOKEN>
```

### Login Endpoint
- **POST** `/api/v1/login`
- **Body:**
  ```json
  {
    "email": "string",
    "password": "string",
    "rememberMe": "boolean" (optional, default: false)
  }
  ```
- **Response:**
  ```json
  {
    "message": "Login successful",
    "token": "jwt_token_string",
    "user": {
      "id": "number",
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "string",
      "organization": "object",
      "departments": "array"
    }
  }
  ```

### Registration Endpoint
- **POST** `/api/v1/register`
- **Body:**
  ```json
  {
    "email": "string",
    "password": "string",
    "firstName": "string",
    "lastName": "string",
    "phone": "string" (optional),
    "organizationName": "string"
  }
  ```
- **Response:**
  ```json
  {
    "message": "User registered successfully",
    "data": {
      "user": "object",
      "organization": "object",
      "organizationChat": "object"
    }
  }
  ```

## User Management

### Get Current User Profile
- **GET** `/api/v1/me`
- **Response:** Detailed user information including organizations, departments, and role permissions

### Update User Profile
- **PATCH** `/api/v1/me`
- **Body:**
  ```json
  {
    "firstName": "string" (optional),
    "lastName": "string" (optional),
    "phone": "string" (optional),
    "imageUrl": "string" (optional),
    "currentPassword": "string" (required for password change),
    "newPassword": "string" (optional)
  }
  ```

### Change Password
- **POST** `/api/v1/change-password`
- **Body:**
  ```json
  {
    "currentPassword": "string",
    "newPassword": "string"
  }
  ```

### User Management
- **GET** `/api/v1/user/{id}`
- **PATCH** `/api/v1/user/{id}`
- **POST** `/api/v1/user/restore`

## Organization Management

### List Organizations
- **GET** `/api/v1/organization`
- **Query Parameters:**
  - `id`: Get specific organization by ID
- **Response:** List of organizations where user has admin privileges

### Create Organization
- **POST** `/api/v1/organization`
- **Body:**
  ```json
  {
    "name": "string",
    "description": "string" (optional),
    "imageUrl": "string" (optional)
  }
  ```
- **Permission:** Only owners can create organizations

### Update Organization
- **PATCH** `/api/v1/organization`
- **Body:**
  ```json
  {
    "id": "number",
    "name": "string" (optional),
    "description": "string" (optional),
    "imageUrl": "string" (optional)
  }
  ```

### Delete Organization
- **DELETE** `/api/v1/organization?id={id}`
- **Note:** Cannot delete organizations with existing departments

### Organization Admin Management
- **GET** `/api/v1/organization-admin/available-users`
- **POST** `/api/v1/organization-admin`

## Department Management

### List Departments
- **GET** `/api/v1/department`
- **Query Parameters:**
  - `organizationId`: Filter by organization ID
  - `id`: Get specific department by ID

### Create Department
- **POST** `/api/v1/department`
- **Body:**
  ```json
  {
    "organizationId": "number",
    "name": "string",
    "description": "string" (optional)
  }
  ```
- **Note:** Automatically creates department chat

### Update Department
- **PATCH** `/api/v1/department`
- **Body:**
  ```json
  {
    "id": "number",
    "name": "string" (optional),
    "description": "string" (optional)
  }
  ```

### Delete Department
- **DELETE** `/api/v1/department?id={id}`
- **Note:** Cannot delete departments with existing members

## Task Management

### List Tasks
- **GET** `/api/v1/task`
- **Query Parameters:**
  - `id`: Get specific task by ID
  - `assignedToUserIds`: Filter by assigned users (comma-separated)
  - `createdByUserId`: Filter by creator
  - `statusId`: Filter by status
  - `organizationId`: Filter by organization
  - `departmentId`: Filter by department

### Create Task
- **POST** `/api/v1/task`
- **Body:**
  ```json
  {
    "taskTitle": "string",
    "taskDescription": "string" (optional),
    "taskAssignments": [
      {
        "userId": "number",
        "isLeader": "boolean"
      }
    ],
    "statusId": "number",
    "points": "number" (optional),
    "dueDate": "string" (ISO date, optional),
    "organizationId": "number",
    "departmentId": "number"
  }
  ```

### Update Task
- **PATCH** `/api/v1/task`
- **Body:** Same as create task with `id` field required

### Delete Task
- **DELETE** `/api/v1/task?id={id}`

### Task Status Management
- **GET** `/api/v1/task-status`
- **POST** `/api/v1/task-status`

### Task Progress Management
- **GET** `/api/v1/task-progress`
- **POST** `/api/v1/task-progress`

### Task Assignment Management
- **GET** `/api/v1/task-assignment-users`

## Chat System

### List Chats
- **GET** `/api/v1/chat`
- **Query Parameters:**
  - `id`: Get specific chat by ID
  - `chatType`: Filter by chat type (private, task, department, organization)
  - `userId`: Filter by user participation
  - `organizationId`: Filter by organization
  - `departmentId`: Filter by department
  - `taskId`: Filter by task

### Create Chat
- **POST** `/api/v1/chat`
- **Body:**
  ```json
  {
    "name": "string" (optional for group chats),
    "chatType": "private|task|department|organization",
    "organizationId": "number" (required for organization/department chats),
    "departmentId": "number" (required for department chats),
    "taskId": "number" (required for task chats),
    "participantIds": ["number"] (for private chats),
    "isBot": "boolean" (optional, default: false),
    "botDuration": "number" (optional, 1-480 minutes)
  }
  ```

### Update Chat
- **PATCH** `/api/v1/chat`
- **Body:**
  ```json
  {
    "id": "number",
    "name": "string" (optional),
    "isActive": "boolean" (optional),
    "isBot": "boolean" (optional),
    "botDuration": "number" (optional)
  }
  ```

### Delete Chat
- **DELETE** `/api/v1/chat?id={id}`

### Chat Messages
- **GET** `/api/v1/chat-message`
- **Query Parameters:**
  - `id`: Get specific message by ID
  - `chatId`: Get messages for chat
  - `page`: Page number (default: 1)
  - `limit`: Messages per page (default: 50, max: 100)
  - `before`: Get messages before message ID
  - `after`: Get messages after message ID
  - `includeReadStatus`: Include detailed read status

### Send Message
- **POST** `/api/v1/chat-message`
- **Body:**
  ```json
  {
    "chatId": "number",
    "content": "string",
    "messageType": "TEXT|IMAGE|FILE|STICKER" (default: TEXT)
  }
  ```

### Update Message
- **PATCH** `/api/v1/chat-message`
- **Body:**
  ```json
  {
    "id": "number",
    "messageStatus": "DELIVERED|READ|FAILED"
  }
  ```

### Delete Message
- **DELETE** `/api/v1/chat-message?id={id}`

### Message Read Status
- **POST** `/api/v1/chat-message/mark-read`
- **GET** `/api/v1/chat-message/read-status`
- **GET** `/api/v1/chat-message/unread-count`

### Chat User Management
- **GET** `/api/v1/chat-user`
- **POST** `/api/v1/chat-user`

### Chat Modals
- **GET** `/api/v1/chat-modal/departments`
- **GET** `/api/v1/chat-modal/organizations`
- **GET** `/api/v1/chat-modal/users`

## Notifications

### List Notifications
- **GET** `/api/v1/notification`
- **Query Parameters:**
  - `page`: Page number (default: 1)
  - `limit`: Notifications per page (default: 10)
  - `isRead`: Filter by read status
  - `isArchived`: Filter by archived status
  - `typeId`: Filter by notification type

### Create Notification
- **POST** `/api/v1/notification`
- **Body:**
  ```json
  {
    "typeId": "number",
    "title": "string",
    "content": "string",
    "data": "object" (optional),
    "entityType": "string" (optional),
    "entityId": "number" (optional),
    "userIds": ["number"] (optional)
  }
  ```

### Mark as Read
- **POST** `/api/v1/notification/read`

### Archive Notification
- **POST** `/api/v1/notification/archive`

### Notification Types
- **GET** `/api/v1/notification-type`

## Feedback System

### List Feedback
- **GET** `/api/v1/feedback`
- **Query Parameters:**
  - `id`: Get specific feedback by ID
  - `view`: View type (`my` for user's feedback, `all` for admin view)
  - `organizationId`: Filter by organization (for admin view)
  - `departmentId`: Filter by department (for admin view)

### Create Feedback
- **POST** `/api/v1/feedback`
- **Body:**
  ```json
  {
    "feedbackTypeId": "number",
    "situation": "string",
    "behavior": "string",
    "impact": "string",
    "actionable": "string",
    "appreciation": "string",
    "growthToken": "number",
    "userIds": ["number"],
    "organizationId": "number" (optional),
    "departmentId": "number" (optional),
    "taskId": "number" (optional)
  }
  ```

### Update Feedback
- **PUT** `/api/v1/feedback`
- **Body:** Same as create feedback with `id` field required

### Delete Feedback
- **DELETE** `/api/v1/feedback?id={id}`

### Feedback Types
- **GET** `/api/v1/feedback-type`

### Feedback Users
- **GET** `/api/v1/feedback-user`

### Feedback Share
- **GET** `/api/v1/feedback-share`
- **POST** `/api/v1/feedback-share`

## Member Management

### List Members
- **GET** `/api/v1/member`

### Delete Member
- **DELETE** `/api/v1/member/delete`

## Assistant Features

### Assistant Chat User
- **GET** `/api/v1/assistant-chat-user`
- **POST** `/api/v1/assistant-chat-user`

### Assistant Messages
- **GET** `/api/v1/assistant-message`
- **POST** `/api/v1/assistant-message`

### Assistant Message Types
- **GET** `/api/v1/assistant-message-type`

## File Management

### File Upload
- **POST** `/api/v1/upload`
- **DELETE** `/api/v1/upload/delete`

## Points and Ranking

### Point Transactions
- **GET** `/api/v1/point-transaction`
- **POST** `/api/v1/point-transaction`

### Ranking
- **GET** `/api/v1/ranking`

## Sync Services

### Admin Chat Sync
- **POST** `/api/v1/admin-chat-sync`

### Department Chat Sync
- **POST** `/api/v1/department-chat-sync`

### Chat Membership Repair
- **POST** `/api/v1/chat-membership-repair`

## Webhooks

### Message Created Webhook
- **POST** `/api/v1/webhook/message-created`

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict
- `500`: Internal Server Error

## Rate Limiting

API endpoints implement standard rate limiting practices. Specific limits depend on the endpoint and user role.

## Permissions

The API implements role-based access control with the following roles:
- **Owner**: Full system access
- **Admin**: Organization-level administration
- **Member**: Limited access to assigned resources
- **Bot**: Automated system access

Permission checks are enforced at the endpoint level based on:
- User role
- Organization membership
- Department leadership
- Resource ownership
- Task assignments

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (varies by endpoint)

Pagination responses include:
```json
{
  "pagination": {
    "page": "number",
    "limit": "number",
    "totalCount": "number",
    "totalPages": "number",
    "hasMore": "boolean"
  }
}
```

## Real-time Features

The chat system supports real-time messaging with WebSocket connections. Message delivery status tracking is implemented through the message read/unread system.

## Data Models

### User
- Basic profile information
- Role assignments
- Organization and department memberships
- Authentication credentials

### Organization
- Company/organization structure
- Owner relationships
- Department hierarchies

### Department
- Team-level organization
- Member assignments
- Leadership roles

### Task
- Work item management
- Assignment tracking
- Progress monitoring
- Status workflows

### Chat
- Multi-type chat system (private, task, department, organization)
- Message history
- Participant management
- Bot integration

### Feedback
- Performance feedback system
- Multi-type feedback (organization, department, private)
- User assignments and sharing

### Notification
- System notifications
- User-specific delivery
- Read/archive status

This API provides a comprehensive platform for team collaboration, task management, and organizational communication.