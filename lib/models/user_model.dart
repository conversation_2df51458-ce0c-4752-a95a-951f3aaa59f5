class UserModel {
  final int id;
  final String email;
  final String firstName;
  final String lastName;
  final String role;
  final String? phone;
  final String? imageUrl;
  final Map<String, dynamic>? organization;
  final List<dynamic>? departments;

  UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.phone,
    this.imageUrl,
    this.organization,
    this.departments,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: _parseIntSafely(json['id']) ?? 0,
      email: json['email']?.toString() ?? '',
      firstName: json['firstName']?.toString() ?? '',
      lastName: json['lastName']?.toString() ?? '',
      role: json['role']?.toString() ?? 'member',
      phone: json['phone']?.toString(),
      imageUrl: json['imageUrl']?.toString(),
      organization: json['organization'] as Map<String, dynamic>?,
      departments: json['departments'] as List<dynamic>?,
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'role': role,
      'phone': phone,
      'imageUrl': imageUrl,
      'organization': organization,
      'departments': departments,
    };
  }

  String get fullName => '$firstName $lastName';
  
  String get displayRole {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'เจ้าของ';
      case 'admin':
        return 'ผู้ดูแลระบบ';
      case 'member':
        return 'สมาชิก';
      case 'bot':
        return 'บอท';
      default:
        return role;
    }
  }

  @override
  String toString() {
    return 'UserModel{id: $id, email: $email, firstName: $firstName, lastName: $lastName, role: $role, phone: $phone}';
  }
}