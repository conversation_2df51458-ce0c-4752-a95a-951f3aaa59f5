class CounterModel {
  final int value;
  final DateTime lastUpdated;

  CounterModel({
    required this.value,
    required this.lastUpdated,
  });

  CounterModel copyWith({
    int? value,
    DateTime? lastUpdated,
  }) {
    return CounterModel(
      value: value ?? this.value,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory CounterModel.fromJson(Map<String, dynamic> json) {
    return CounterModel(
      value: json['value'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}