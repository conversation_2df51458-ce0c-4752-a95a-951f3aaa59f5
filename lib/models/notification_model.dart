class NotificationModel {
  final int id;
  final int userId;
  final int notificationId;
  final bool isRead;
  final DateTime? readAt;
  final bool isArchived;
  final DateTime? archivedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final NotificationDetailModel? notification;

  // Legacy properties for backward compatibility
  int get typeId => notification?.typeId ?? 0;
  String get title => notification?.title ?? '';
  String get content => notification?.content ?? '';
  Map<String, dynamic>? get data => notification?.data;
  String? get entityType => notification?.entityType;
  int? get entityId => notification?.entityId;
  NotificationTypeModel? get type => notification?.type;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.notificationId,
    required this.isRead,
    this.readAt,
    required this.isArchived,
    this.archivedAt,
    required this.createdAt,
    required this.updatedAt,
    this.notification,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: _parseIntSafely(json['id']) ?? 0,
      userId: _parseIntSafely(json['userId']) ?? 0,
      notificationId: _parseIntSafely(json['notificationId']) ?? 0,
      isRead: json['isRead'] == true,
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
      isArchived: json['isArchived'] == true,
      archivedAt: json['archivedAt'] != null ? DateTime.parse(json['archivedAt']) : null,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      notification: json['notification'] != null ? NotificationDetailModel.fromJson(json['notification']) : null,
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'notificationId': notificationId,
      'isRead': isRead,
      'readAt': readAt?.toIso8601String(),
      'isArchived': isArchived,
      'archivedAt': archivedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'notification': notification?.toJson(),
    };
  }

  // Helper method to get formatted time
  String get formattedTime {
    final now = DateTime.now();
    final targetTime = notification?.createdAt ?? createdAt;
    final difference = now.difference(targetTime);

    if (difference.inMinutes < 1) {
      return 'เมื่อสักครู่';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} นาทีที่แล้ว';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ชั่วโมงที่แล้ว';
    } else if (difference.inDays == 1) {
      return 'เมื่อวาน';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} วันที่แล้ว';
    } else {
      return '${targetTime.day}/${targetTime.month}/${targetTime.year}';
    }
  }

  @override
  String toString() {
    return 'NotificationModel{id: $id, userId: $userId, notificationId: $notificationId, title: $title, isRead: $isRead}';
  }
}

class NotificationDetailModel {
  final int id;
  final int typeId;
  final String title;
  final String content;
  final Map<String, dynamic>? data;
  final String? entityType;
  final int? entityId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final NotificationTypeModel? type;

  NotificationDetailModel({
    required this.id,
    required this.typeId,
    required this.title,
    required this.content,
    this.data,
    this.entityType,
    this.entityId,
    required this.createdAt,
    required this.updatedAt,
    this.type,
  });

  factory NotificationDetailModel.fromJson(Map<String, dynamic> json) {
    return NotificationDetailModel(
      id: _parseIntSafely(json['id']) ?? 0,
      typeId: _parseIntSafely(json['typeId']) ?? 0,
      title: json['title']?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      data: json['data'] as Map<String, dynamic>?,
      entityType: json['entityType']?.toString(),
      entityId: _parseIntSafely(json['entityId']),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      type: json['type'] != null ? NotificationTypeModel.fromJson(json['type']) : null,
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'typeId': typeId,
      'title': title,
      'content': content,
      'data': data,
      'entityType': entityType,
      'entityId': entityId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'type': type?.toJson(),
    };
  }
}

class NotificationTypeModel {
  final int id;
  final String name;
  final String? displayName;
  final String? description;
  final String? template;
  final String? color;
  final bool? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  NotificationTypeModel({
    required this.id,
    required this.name,
    this.displayName,
    this.description,
    this.template,
    this.color,
    this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  factory NotificationTypeModel.fromJson(Map<String, dynamic> json) {
    return NotificationTypeModel(
      id: _parseIntSafely(json['id']) ?? 0,
      name: json['name']?.toString() ?? '',
      displayName: json['displayName']?.toString(),
      description: json['description']?.toString(),
      template: json['template']?.toString(),
      color: json['color']?.toString(),
      isActive: json['isActive'] as bool?,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'description': description,
      'template': template,
      'color': color,
      'isActive': isActive,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

class NotificationListResponse {
  final List<NotificationModel> notifications;
  final PaginationModel pagination;

  NotificationListResponse({
    required this.notifications,
    required this.pagination,
  });

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) {
    List<NotificationModel> notificationList = [];
    
    if (json['data'] is List) {
      notificationList = (json['data'] as List<dynamic>)
          .map((item) => NotificationModel.fromJson(item as Map<String, dynamic>))
          .toList();
    }
    
    return NotificationListResponse(
      notifications: notificationList,
      pagination: PaginationModel.fromJson(json['pagination'] ?? {}),
    );
  }
}

class PaginationModel {
  final int page;
  final int limit;
  final int totalCount;
  final int totalPages;
  final bool hasMore;

  PaginationModel({
    required this.page,
    required this.limit,
    required this.totalCount,
    required this.totalPages,
    required this.hasMore,
  });

  factory PaginationModel.fromJson(Map<String, dynamic> json) {
    return PaginationModel(
      page: json['page'] ?? 1,
      limit: json['limit'] ?? 10,
      totalCount: json['totalCount'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      hasMore: json['hasMore'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'limit': limit,
      'totalCount': totalCount,
      'totalPages': totalPages,
      'hasMore': hasMore,
    };
  }
}