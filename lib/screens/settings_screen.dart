import 'package:flutter/material.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';
import 'package:hia_sang_ma/constants/app_strings.dart';
import 'package:hia_sang_ma/services/auth_service.dart';
import 'package:hia_sang_ma/models/user_model.dart';
import 'package:hia_sang_ma/widgets/change_password_dialog.dart';
import 'package:hia_sang_ma/widgets/edit_profile_dialog.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;
  bool _soundEnabled = true;
  
  final AuthService _authService = AuthService();
  UserModel? _currentUser;
  bool _isLoadingUser = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    print('🔍 SettingsScreen: Starting to load user profile...');
    
    try {
      // First try to get the real user profile from AuthService
      final user = await _authService.getCurrentUser();
      if (user != null) {
        print('🔍 SettingsScreen: Found existing user: ${user.fullName}');
        setState(() {
          _currentUser = user;
          _isLoadingUser = false;
        });
        return;
      }
    } catch (e) {
      print('🔍 SettingsScreen: Error getting current user: $e');
    }
    
    // If no real user found, create a mock user for testing
    final mockUser = UserModel(
      id: 1,
      email: '<EMAIL>',
      firstName: 'ผู้ใช้',
      lastName: 'ตัวอย่าง',
      role: 'member',
      phone: '0123456789',
    );
    
    print('🔍 SettingsScreen: Setting mock user: ${mockUser.fullName}');
    
    if (mounted) {
      setState(() {
        _currentUser = mockUser;
        _isLoadingUser = false;
      });
      print('🔍 SettingsScreen: Mock user set successfully');
    }
  }

  void _onProfileUpdated(UserModel updatedUser) {
    setState(() {
      _currentUser = updatedUser;
    });
  }

  void _showChangePasswordDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const ChangePasswordDialog(),
    );
  }

  void _showEditProfileDialog() {
    if (_currentUser != null) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) => EditProfileDialog(
          user: _currentUser!,
          onProfileUpdated: _onProfileUpdated,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🔍 SettingsScreen: Building widget...');
    print('🔍 SettingsScreen: _isLoadingUser = $_isLoadingUser');
    print('🔍 SettingsScreen: _currentUser = $_currentUser');
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: Text(AppStrings.settingsTitle),
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Profile Section
          _buildSectionHeader('โปรไฟล์'),
          const SizedBox(height: 8),
          _isLoadingUser 
            ? Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(child: CircularProgressIndicator()),
              )
            : _buildProfileCard(),
          const SizedBox(height: 24),
          
          
          // Account Section
          _buildSectionHeader('บัญชี'),
          const SizedBox(height: 8),
          _buildSettingItem(
            icon: Icons.person,
            title: 'แก้ไขโปรไฟล์',
            subtitle: 'เปลี่ยนข้อมูลส่วนตัว',
            trailing: const Icon(Icons.chevron_right),
            onTap: _showEditProfileDialog,
          ),
          const SizedBox(height: 12),
          _buildSettingItem(
            icon: Icons.lock,
            title: 'เปลี่ยนรหัสผ่าน',
            subtitle: 'อัพเดทรหัสผ่านของคุณ',
            trailing: const Icon(Icons.chevron_right),
            onTap: _showChangePasswordDialog,
          ),
          const SizedBox(height: 12),
          const SizedBox(height: 24),
          
          // Support Section
          _buildSectionHeader('การสนับสนุน'),
          const SizedBox(height: 8),
          _buildSettingItem(
            icon: Icons.help,
            title: 'ช่วยเหลือ',
            subtitle: 'คำถามที่พบบ่อยและคู่มือ',
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // Help
            },
          ),
          const SizedBox(height: 12),
          _buildSettingItem(
            icon: Icons.feedback,
            title: 'ติชม',
            subtitle: 'ส่งความคิดเห็นของคุณ',
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // Feedback
            },
          ),
          const SizedBox(height: 12),
          _buildSettingItem(
            icon: Icons.info,
            title: 'เกี่ยวกับ',
            subtitle: 'เวอร์ชัน 2.1.0',
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // About
            },
          ),
          const SizedBox(height: 24),
          
          // Logout Button
          _buildLogoutButton(),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.grey[800],
      ),
    );
  }

  Widget _buildProfileCard() {
    print('🔍 SettingsScreen: Building profile card...');
    print('🔍 SettingsScreen: Current user: $_currentUser');
    print('🔍 SettingsScreen: Is loading: $_isLoadingUser');
    
    final user = _currentUser;
    if (user == null) {
      print('🔍 SettingsScreen: User is null, showing error message');
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text('ไม่สามารถโหลดข้อมูลผู้ใช้ได้'),
      );
    }
    
    print('🔍 SettingsScreen: User found, building profile card for: ${user.fullName}');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32,
            backgroundColor: AppColors.primary,
            backgroundImage: user.imageUrl != null ? NetworkImage(user.imageUrl!) : null,
            child: user.imageUrl == null
                ? Text(
                    user.firstName.isNotEmpty ? user.firstName[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.fullName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user.email,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (user.phone != null && user.phone!.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    user.phone!,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.displayRole,
                    style: TextStyle(
                      color: Colors.green[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _showEditProfileDialog,
            icon: const Icon(Icons.edit),
            color: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(icon, color: AppColors.primary),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(color: Colors.grey[600]),
        ),
        trailing: trailing,
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: () {
          // Show logout confirmation bottom sheet
          showModalBottomSheet(
            context: context,
            backgroundColor: Colors.white,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            builder: (context) => Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'ออกจากระบบ',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'คุณต้องการออกจากระบบหรือไม่?',
                    style: TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('ยกเลิก'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            Navigator.of(context).pop();
                            try {
                              await _authService.logoutAndRedirect();
                              // No need to show success message since we're redirecting
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('${AppStrings.logoutFailed}: ${e.toString()}'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('ออกจากระบบ'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          );
        },
        icon: const Icon(Icons.logout),
        label: Text(AppStrings.logout),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}