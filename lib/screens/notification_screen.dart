import 'package:flutter/material.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';
import 'package:hia_sang_ma/constants/app_strings.dart';
import 'package:hia_sang_ma/services/notification_service.dart';
import 'package:hia_sang_ma/services/auth_service.dart';
import 'package:hia_sang_ma/models/notification_model.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final NotificationService _notificationService = NotificationService();
  final AuthService _authService = AuthService();
  final ScrollController _scrollController = ScrollController();

  List<NotificationModel> _notifications = [];
  List<NotificationTypeModel> _notificationTypes = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  int _currentPage = 1;
  bool _hasMore = true;
  String? _selectedFilter = 'all';
  int? _selectedTypeFilter;

  @override
  void initState() {
    super.initState();
    _loadData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMore) {
      _loadMoreNotifications();
    }
  }

  Future<void> _loadData() async {
    await Future.wait([
      _loadNotifications(refresh: true),
      _loadNotificationTypes(),
    ]);
  }

  Future<void> _loadNotifications({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _isLoading = true;
        _currentPage = 1;
        _hasMore = true;
      });
    }

    try {
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      bool? isRead;
      if (_selectedFilter == 'unread') isRead = false;
      if (_selectedFilter == 'read') isRead = true;

      final response = await _notificationService.getNotifications(
        token: token,
        page: _currentPage,
        limit: 20,
        isRead: isRead,
        typeId: _selectedTypeFilter,
      );

      if (mounted) {
        setState(() {
          if (refresh) {
            _notifications = response.notifications;
          } else {
            _notifications.addAll(response.notifications);
          }
          _hasMore = response.pagination.hasMore;
          _isLoading = false;
        });

        if (response.notifications.isNotEmpty) {
          print(
            '🔍 First notification: ${response.notifications.first.toString()}',
          );
          print('🔍 Title: "${response.notifications.first.title}"');
          print('🔍 Content: "${response.notifications.first.content}"');
        }
      }
    } catch (e) {
      print('Error loading notifications: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          if (refresh) {
            _notifications = [];
          }
        });
        _showError('ไม่สามารถโหลดการแจ้งเตือนได้: ${e.toString()}');
      }
    }
  }

  Future<void> _loadNotificationTypes() async {
    try {
      final token = await _authService.getToken();
      final types = await _notificationService.getNotificationTypes(
        token: token,
      );
      if (mounted) {
        setState(() {
          _notificationTypes = types;
        });
      }
    } catch (e) {
      print('Error loading notification types: $e');
    }
  }

  Future<void> _loadMoreNotifications() async {
    if (_isLoadingMore || !_hasMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    try {
      final token = await _authService.getToken();
      if (token == null) throw Exception('No token');

      bool? isRead;
      if (_selectedFilter == 'unread') isRead = false;
      if (_selectedFilter == 'read') isRead = true;

      final response = await _notificationService.getNotifications(
        token: token,
        page: _currentPage,
        limit: 20,
        isRead: isRead,
        typeId: _selectedTypeFilter,
      );

      if (mounted) {
        setState(() {
          _notifications.addAll(response.notifications);
          _hasMore = response.pagination.hasMore;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error loading more notifications: $e');
      setState(() {
        _currentPage--;
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: Text(AppStrings.notificationTitle),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            onPressed: _markAllAsRead,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => _loadNotifications(refresh: true),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildNotificationsList(),
      ),
    );
  }

  Widget _buildNotificationsList() {
    if (_notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.notifications_none, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'ไม่มีการแจ้งเตือน',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'การแจ้งเตือนจะแสดงที่นี่',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16.0),
      itemCount: _notifications.length + (_isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= _notifications.length) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final notification = _notifications[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildNotificationItem(notification),
        );
      },
    );
  }

  Widget _buildNotificationItem(NotificationModel notification) {
    final iconData = _getNotificationIcon(notification);
    final iconColor = _getNotificationColor(notification);

    return Dismissible(
      key: Key('notification_${notification.id}'),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(Icons.archive, color: Colors.white, size: 24),
      ),
      confirmDismiss: (direction) async {
        return await _showArchiveConfirmation(notification);
      },
      onDismissed: (direction) {
        _archiveNotification(notification);
      },
      child: GestureDetector(
        onTap: () => _onNotificationTap(notification),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: notification.isRead ? Colors.white : Colors.blue[50],
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
            border: notification.isRead
                ? null
                : Border.all(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    width: 1,
                  ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(iconData, color: iconColor, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: TextStyle(
                              fontWeight: notification.isRead
                                  ? FontWeight.w600
                                  : FontWeight.bold,
                              fontSize: 16,
                              color: notification.isRead
                                  ? Colors.grey[800]
                                  : Colors.black,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.content.isEmpty
                          ? 'ไม่มีรายละเอียด'
                          : notification.content,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          notification.formattedTime,
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                        if (notification.type != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: iconColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              notification.type!.displayName ??
                                  notification.type!.name,
                              style: TextStyle(
                                color: iconColor,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationModel notification) {
    final typeName = notification.type?.name.toLowerCase() ?? '';

    if (typeName.contains('task') || typeName.contains('งาน')) {
      return Icons.assignment_turned_in;
    } else if (typeName.contains('chat') ||
        typeName.contains('message') ||
        typeName.contains('ข้อความ')) {
      return Icons.chat;
    } else if (typeName.contains('user') ||
        typeName.contains('member') ||
        typeName.contains('สมาชิก')) {
      return Icons.person_add;
    } else if (typeName.contains('meeting') ||
        typeName.contains('event') ||
        typeName.contains('ประชุม')) {
      return Icons.event;
    } else if (typeName.contains('update') || typeName.contains('อัพเดท')) {
      return Icons.update;
    } else if (typeName.contains('warning') || typeName.contains('เตือน')) {
      return Icons.warning;
    } else if (typeName.contains('success') ||
        typeName.contains('complete') ||
        typeName.contains('สำเร็จ')) {
      return Icons.check_circle;
    } else {
      return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationModel notification) {
    final typeName = notification.type?.name.toLowerCase() ?? '';

    if (typeName.contains('task') || typeName.contains('งาน')) {
      return Colors.green;
    } else if (typeName.contains('chat') ||
        typeName.contains('message') ||
        typeName.contains('ข้อความ')) {
      return Colors.blue;
    } else if (typeName.contains('user') ||
        typeName.contains('member') ||
        typeName.contains('สมาชิก')) {
      return Colors.purple;
    } else if (typeName.contains('meeting') ||
        typeName.contains('event') ||
        typeName.contains('ประชุม')) {
      return Colors.orange;
    } else if (typeName.contains('update') || typeName.contains('อัพเดท')) {
      return Colors.indigo;
    } else if (typeName.contains('warning') || typeName.contains('เตือน')) {
      return Colors.red;
    } else if (typeName.contains('success') ||
        typeName.contains('complete') ||
        typeName.contains('สำเร็จ')) {
      return Colors.green;
    } else {
      return AppColors.primary;
    }
  }

  Future<void> _onNotificationTap(NotificationModel notification) async {
    if (!notification.isRead) {
      await _markAsRead(notification);
    }

    if (notification.entityType != null && notification.entityId != null) {
      print('Navigate to ${notification.entityType} ${notification.entityId}');
    }
  }

  Future<void> _markAsRead(NotificationModel notification) async {
    try {
      final token = await _authService.getToken();
      if (token == null) return;

      await _notificationService.markAsRead(
        token: token,
        notificationId: notification.id,
      );

      setState(() {
        final index = _notifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _notifications[index] = NotificationModel(
            id: notification.id,
            userId: notification.userId,
            notificationId: notification.notificationId,
            isRead: true,
            readAt: DateTime.now(),
            isArchived: notification.isArchived,
            archivedAt: notification.archivedAt,
            createdAt: notification.createdAt,
            updatedAt: notification.updatedAt,
            notification: notification.notification,
          );
        }
      });
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      final token = await _authService.getToken();
      if (token == null) return;

      await _notificationService.markAllAsRead(token: token);
      await _loadNotifications(refresh: true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ทำการอ่านทั้งหมดแล้ว'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showError('ไม่สามารถทำการอ่านทั้งหมดได้: ${e.toString()}');
    }
  }

  Future<bool> _showArchiveConfirmation(NotificationModel notification) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('เก็บถาวร'),
            content: const Text('คุณต้องการเก็บการแจ้งเตือนนี้ถาวรหรือไม่?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('ยกเลิก'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('เก็บถาวร'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _archiveNotification(NotificationModel notification) async {
    try {
      final token = await _authService.getToken();
      if (token == null) return;

      await _notificationService.archiveNotification(
        token: token,
        notificationId: notification.id,
      );

      setState(() {
        _notifications.removeWhere((n) => n.id == notification.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('เก็บถาวรแล้ว'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showError('ไม่สามารถเก็บถาวรได้: ${e.toString()}');
      _loadNotifications(refresh: true);
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'กรองการแจ้งเตือน',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 24),
            const Text(
              'สถานะการอ่าน',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('ทั้งหมด'),
                  selected: _selectedFilter == 'all',
                  onSelected: (selected) {
                    if (selected) {
                      setState(() => _selectedFilter = 'all');
                      Navigator.pop(context);
                      _loadNotifications(refresh: true);
                    }
                  },
                ),
                FilterChip(
                  label: const Text('ยังไม่อ่าน'),
                  selected: _selectedFilter == 'unread',
                  onSelected: (selected) {
                    if (selected) {
                      setState(() => _selectedFilter = 'unread');
                      Navigator.pop(context);
                      _loadNotifications(refresh: true);
                    }
                  },
                ),
                FilterChip(
                  label: const Text('อ่านแล้ว'),
                  selected: _selectedFilter == 'read',
                  onSelected: (selected) {
                    if (selected) {
                      setState(() => _selectedFilter = 'read');
                      Navigator.pop(context);
                      _loadNotifications(refresh: true);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            if (_notificationTypes.isNotEmpty) ...[
              const Text(
                'ประเภทการแจ้งเตือน',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: [
                  FilterChip(
                    label: const Text('ทั้งหมด'),
                    selected: _selectedTypeFilter == null,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() => _selectedTypeFilter = null);
                        Navigator.pop(context);
                        _loadNotifications(refresh: true);
                      }
                    },
                  ),
                  ..._notificationTypes.map(
                    (type) => FilterChip(
                      label: Text(type.displayName ?? type.name),
                      selected: _selectedTypeFilter == type.id,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() => _selectedTypeFilter = type.id);
                          Navigator.pop(context);
                          _loadNotifications(refresh: true);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }
}
