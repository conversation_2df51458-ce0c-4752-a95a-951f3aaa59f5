class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  final Map<String, dynamic> _storage = {};

  void save(String key, dynamic value) {
    _storage[key] = value;
  }

  T? get<T>(String key) {
    return _storage[key] as T?;
  }

  bool contains<PERSON>ey(String key) {
    return _storage.containsKey(key);
  }

  void remove(String key) {
    _storage.remove(key);
  }

  void clear() {
    _storage.clear();
  }

  Map<String, dynamic> getAll() {
    return Map.from(_storage);
  }
}