import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

class ApiService {
  static const String baseUrl = 'https://hsm.lucablock.io/api/v1';
  static const Duration timeout = Duration(seconds: 30);

  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };

  static void _logRequest(
    String method,
    String url,
    Map<String, String> headers,
    String? body,
  ) {
    print('🌐 API Request: $method $url');
    print('📋 Headers: $headers');
    if (body != null) {
      print('📦 Body: $body');
    }
  }

  static void _logResponse(http.Response response) {
    print('📨 Response: ${response.statusCode}');
    print('📄 Body: ${response.body}');
  }

  static Future<http.Response> post(
    String endpoint,
    Map<String, dynamic> body, {
    String? token,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final headers = token != null ? getAuthHeaders(token) : defaultHeaders;
    final bodyJson = json.encode(body);

    _logRequest('POST', url.toString(), headers, bodyJson);

    try {
      final response = await http
          .post(url, headers: headers, body: bodyJson)
          .timeout(timeout);

      _logResponse(response);
      return response;
    } on SocketException {
      throw Exception(
        'No internet connection. Please check your network and try again.',
      );
    } on HttpException {
      throw Exception('HTTP error occurred. Please try again.');
    } on FormatException {
      throw Exception('Bad response format from server.');
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } catch (e) {
      print('❌ Network error: $e');
      throw Exception('Network error: ${e.toString()}');
    }
  }

  static Future<http.Response> get(
    String endpoint, {
    String? token,
    Map<String, String>? queryParams,
  }) async {
    Uri url = Uri.parse('$baseUrl$endpoint');
    if (queryParams != null) {
      url = url.replace(queryParameters: queryParams);
    }

    final headers = token != null ? getAuthHeaders(token) : defaultHeaders;

    _logRequest('GET', url.toString(), headers, null);

    try {
      final response = await http.get(url, headers: headers).timeout(timeout);
      _logResponse(response);
      return response;
    } on SocketException {
      throw Exception(
        'No internet connection. Please check your network and try again.',
      );
    } on HttpException {
      throw Exception('HTTP error occurred. Please try again.');
    } on FormatException {
      throw Exception('Bad response format from server.');
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } catch (e) {
      print('❌ Network error: $e');
      throw Exception('Network error: ${e.toString()}');
    }
  }

  static Future<http.Response> patch(
    String endpoint,
    Map<String, dynamic> body, {
    String? token,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final headers = token != null ? getAuthHeaders(token) : defaultHeaders;
    final bodyJson = json.encode(body);

    _logRequest('PATCH', url.toString(), headers, bodyJson);

    try {
      final response = await http
          .patch(url, headers: headers, body: bodyJson)
          .timeout(timeout);
      _logResponse(response);
      return response;
    } on SocketException {
      throw Exception(
        'No internet connection. Please check your network and try again.',
      );
    } on HttpException {
      throw Exception('HTTP error occurred. Please try again.');
    } on FormatException {
      throw Exception('Bad response format from server.');
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } catch (e) {
      print('❌ Network error: $e');
      throw Exception('Network error: ${e.toString()}');
    }
  }

  static Future<http.Response> delete(String endpoint, {String? token}) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final headers = token != null ? getAuthHeaders(token) : defaultHeaders;

    _logRequest('DELETE', url.toString(), headers, null);

    try {
      final response = await http
          .delete(url, headers: headers)
          .timeout(timeout);
      _logResponse(response);
      return response;
    } on SocketException {
      throw Exception(
        'No internet connection. Please check your network and try again.',
      );
    } on HttpException {
      throw Exception('HTTP error occurred. Please try again.');
    } on FormatException {
      throw Exception('Bad response format from server.');
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } catch (e) {
      print('❌ Network error: $e');
      throw Exception('Network error: ${e.toString()}');
    }
  }
}
