import 'dart:convert';
import 'package:hia_sang_ma/services/api_service.dart';
import 'package:hia_sang_ma/models/notification_model.dart';
import 'package:hia_sang_ma/constants/app_strings.dart';

class NotificationService {
  /// Get list of notifications
  /// Parameters:
  /// - page: Page number (default: 1)
  /// - limit: Notifications per page (default: 10)
  /// - isRead: Filter by read status
  /// - isArchived: Filter by archived status
  /// - typeId: Filter by notification type
  Future<NotificationListResponse> getNotifications({
    String? token,
    int page = 1,
    int limit = 10,
    bool? isRead,
    bool? isArchived,
    int? typeId,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (isRead != null) queryParams['isRead'] = isRead.toString();
      if (isArchived != null) queryParams['isArchived'] = isArchived.toString();
      if (typeId != null) queryParams['typeId'] = typeId.toString();

      final uri = Uri.parse('/notification').replace(queryParameters: queryParams);
      final response = await ApiService.get(uri.toString(), token: token);

      print('🔍 Notifications API response: ${response.statusCode}');
      print('📄 Response body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final responseData = json.decode(response.body);
          return NotificationListResponse.fromJson(responseData);
        } catch (parseError) {
          print('❌ JSON parsing error: $parseError');
          throw Exception('Failed to parse notifications data');
        }
      } else if (response.statusCode == 401) {
        throw Exception(AppStrings.invalidCredentials);
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } catch (e) {
      if (e.toString().contains('Failed to parse notifications data') ||
          e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  /// Mark notification as read
  Future<void> markAsRead({
    required String token,
    required int notificationId,
  }) async {
    try {
      final response = await ApiService.post('/notification/read', {
        'notificationId': notificationId,
      }, token: token);

      print('🔍 Mark as read response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return;
      } else if (response.statusCode == 401) {
        throw Exception(AppStrings.invalidCredentials);
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } catch (e) {
      if (e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead({
    required String token,
  }) async {
    try {
      final response = await ApiService.post('/notification/read', {
        'markAll': true,
      }, token: token);

      print('🔍 Mark all as read response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return;
      } else if (response.statusCode == 401) {
        throw Exception(AppStrings.invalidCredentials);
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } catch (e) {
      if (e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  /// Archive notification
  Future<void> archiveNotification({
    required String token,
    required int notificationId,
  }) async {
    try {
      final response = await ApiService.post('/notification/archive', {
        'notificationId': notificationId,
      }, token: token);

      print('🔍 Archive notification response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return;
      } else if (response.statusCode == 401) {
        throw Exception(AppStrings.invalidCredentials);
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } catch (e) {
      if (e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  /// Get notification types
  Future<List<NotificationTypeModel>> getNotificationTypes({
    String? token,
  }) async {
    try {
      final response = await ApiService.get('/notification-type', token: token);

      print('🔍 Notification types response: ${response.statusCode}');

      if (response.statusCode == 200) {
        try {
          final responseData = json.decode(response.body);
          final typesList = responseData is List ? responseData : responseData['data'] ?? [];
          return typesList
              .map<NotificationTypeModel>((item) => NotificationTypeModel.fromJson(item))
              .toList();
        } catch (parseError) {
          print('❌ JSON parsing error: $parseError');
          throw Exception('Failed to parse notification types data');
        }
      } else if (response.statusCode == 401) {
        throw Exception(AppStrings.invalidCredentials);
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } catch (e) {
      if (e.toString().contains('Failed to parse notification types data') ||
          e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  /// Create notification (for admin/owner roles)
  Future<NotificationModel> createNotification({
    required String token,
    required int typeId,
    required String title,
    required String content,
    Map<String, dynamic>? data,
    String? entityType,
    int? entityId,
    List<int>? userIds,
  }) async {
    try {
      final body = <String, dynamic>{
        'typeId': typeId,
        'title': title,
        'content': content,
      };

      if (data != null) body['data'] = data;
      if (entityType != null) body['entityType'] = entityType;
      if (entityId != null) body['entityId'] = entityId;
      if (userIds != null) body['userIds'] = userIds;

      final response = await ApiService.post('/notification', body, token: token);

      print('🔍 Create notification response: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          final responseData = json.decode(response.body);
          return NotificationModel.fromJson(responseData);
        } catch (parseError) {
          print('❌ JSON parsing error: $parseError');
          throw Exception('Failed to parse created notification data');
        }
      } else if (response.statusCode == 401) {
        throw Exception(AppStrings.invalidCredentials);
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } catch (e) {
      if (e.toString().contains('Failed to parse created notification data') ||
          e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  /// Get unread notification count
  Future<int> getUnreadCount({String? token}) async {
    try {
      // Get first page with unread filter to get total count
      final response = await getNotifications(
        token: token,
        page: 1,
        limit: 1,
        isRead: false,
      );
      
      return response.pagination.totalCount;
    } catch (e) {
      print('❌ Error getting unread count: $e');
      return 0;
    }
  }
}