import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hia_sang_ma/services/api_service.dart';
import 'package:hia_sang_ma/models/user_model.dart';
import 'package:hia_sang_ma/models/login_response.dart';
import 'package:hia_sang_ma/constants/app_strings.dart';
import 'package:hia_sang_ma/screens/login_screen.dart';
import 'package:hia_sang_ma/main.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';

  Future<LoginResponse> login(String email, String password, {bool rememberMe = false}) async {
    try {
      final response = await ApiService.post('/login', {
        'email': email,
        'password': password,
        'rememberMe': rememberMe,
      });

      print('🔍 Login response status: ${response.statusCode}');
      print('🔍 Login response body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> responseData = json.decode(response.body);
          final loginResponse = LoginResponse.fromJson(responseData);
          
          // Save token and user data if login successful
          await _saveAuthData(loginResponse.token, loginResponse.user);
          
          return loginResponse;
        } catch (parseError) {
          print('❌ JSON parsing error: $parseError');
          print('📄 Response body: ${response.body}');
          throw Exception(AppStrings.serverError);
        }
      } else if (response.statusCode == 401) {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.invalidCredentials);
        } catch (_) {
          throw Exception(AppStrings.invalidCredentials);
        }
      } else {
        try {
          final errorData = json.decode(response.body);
          throw Exception(errorData['error'] ?? AppStrings.serverError);
        } catch (_) {
          throw Exception('${AppStrings.serverError} (${response.statusCode})');
        }
      }
    } on Exception catch (e) {
      // Re-throw our custom exceptions
      if (e.toString().contains(AppStrings.invalidCredentials) ||
          e.toString().contains(AppStrings.serverError)) {
        rethrow;
      }
      // Handle network exceptions from ApiService
      rethrow;
    } catch (e) {
      print('❌ Unexpected error in login: $e');
      throw Exception(AppStrings.unexpectedError);
    }
  }

  Future<void> _saveAuthData(String token, UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, token);
      await prefs.setString(_userKey, json.encode(user.toJson()));
    } catch (e) {
      print('❌ Error saving auth data: $e');
      // Don't throw error here - let login continue even if storage fails
    }
  }

  Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      print('❌ Error getting token: $e');
      return null;
    }
  }

  Future<UserModel?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        return UserModel.fromJson(json.decode(userJson));
      }
      return null;
    } catch (e) {
      print('❌ Error getting current user: $e');
      return null;
    }
  }

  Future<bool> isLoggedIn() async {
    try {
      final token = await getToken();
      return token != null && token.isNotEmpty;
    } catch (e) {
      print('❌ Error checking login status: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userKey);
    } catch (e) {
      print('❌ Error during logout: $e');
      // Continue with logout even if storage cleanup fails
    }
  }

  Future<void> logoutAndRedirect() async {
    await logout();
    
    // Import the navigator key from main.dart
    try {
      final context = navigatorKey.currentContext;
      if (context != null) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      print('❌ Error during navigation: $e');
    }
  }

  Future<UserModel> getCurrentUserProfile() async {
    final token = await getToken();
    if (token == null) throw Exception(AppStrings.invalidCredentials);

    try {
      final response = await ApiService.get('/me', token: token);
      
      if (response.statusCode == 200) {
        final userData = json.decode(response.body);
        
        print('🔍 User data from /me API: $userData');
        
        // Check if the response has the expected structure
        if (userData is! Map<String, dynamic>) {
          throw Exception('Invalid response format: expected object, got ${userData.runtimeType}');
        }
        
        try {
          final user = UserModel.fromJson(userData);
          
          // Update stored user data
          await _saveAuthData(token, user);
          
          return user;
        } catch (e) {
          print('❌ Error parsing user data: $e');
          print('📄 Raw data: $userData');
          throw Exception('Failed to parse user data: ${e.toString()}');
        }
      } else if (response.statusCode == 401) {
        await logout();
        throw Exception('${AppStrings.invalidCredentials} (หมดอายุ)');
      } else {
        throw Exception(AppStrings.serverError);
      }
    } catch (e) {
      if (e.toString().contains('Failed to parse user data') ||
          e.toString().contains('Invalid response format')) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  Future<UserModel> updateUserProfile({
    String? firstName,
    String? lastName,
    String? phone,
    String? imageUrl,
  }) async {
    final token = await getToken();
    if (token == null) throw Exception(AppStrings.invalidCredentials);

    try {
      final body = <String, dynamic>{};
      if (firstName != null) body['firstName'] = firstName;
      if (lastName != null) body['lastName'] = lastName;
      if (phone != null) body['phone'] = phone;
      if (imageUrl != null) body['imageUrl'] = imageUrl;

      final response = await ApiService.patch('/me', body, token: token);
      
      if (response.statusCode == 200) {
        final userData = json.decode(response.body);
        final user = UserModel.fromJson(userData);
        
        // Update stored user data
        await _saveAuthData(token, user);
        
        return user;
      } else if (response.statusCode == 401) {
        await logout();
        throw Exception('${AppStrings.invalidCredentials} (หมดอายุ)');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? AppStrings.serverError);
      }
    } catch (e) {
      if (e.toString().contains(AppStrings.invalidCredentials)) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final token = await getToken();
    if (token == null) throw Exception(AppStrings.invalidCredentials);

    try {
      final response = await ApiService.post('/change-password', {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      }, token: token);
      
      if (response.statusCode == 200) {
        // Password changed successfully
        return;
      } else if (response.statusCode == 401) {
        await logout();
        throw Exception('${AppStrings.invalidCredentials} (หมดอายุ)');
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'รหัสผ่านปัจจุบันไม่ถูกต้อง');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? AppStrings.serverError);
      }
    } catch (e) {
      if (e.toString().contains(AppStrings.invalidCredentials) ||
          e.toString().contains('รหัสผ่านปัจจุบันไม่ถูกต้อง')) {
        rethrow;
      }
      throw Exception('${AppStrings.networkError}: ${e.toString()}');
    }
  }
}