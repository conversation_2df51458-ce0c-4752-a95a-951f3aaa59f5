class AppStrings {
  static const String appName = 'เฮีย สั่ง มา';
  static const String counterText = 'คุณกดปุ่มไปแล้วจำนวนครั้ง:';
  static const String incrementTooltip = 'เพิ่ม';

  // Login screen
  static const String email = 'อีเมล';
  static const String password = 'รหัสผ่าน';
  static const String login = 'เข้าสู่ระบบ';
  static const String rememberMe = 'จำฉันไว้';
  static const String apiEndpoint = 'ปลายทาง API:';

  // Validation messages
  static const String pleaseEnterEmail = 'กรุณากรอกอีเมล';
  static const String pleaseEnterValidEmail = 'กรุณากรอกอีเมลที่ถูกต้อง';
  static const String pleaseEnterPassword = 'กรุณากรอกรหัสผ่าน';
  static const String passwordTooShort = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';

  // Home screen
  static const String welcomeUser = 'ยินดีต้อนรับ';
  static const String role = 'บทบาท';
  static const String logout = 'ออกจากระบบ';
  
  // Navigation
  static const String board = 'บอร์ด';
  static const String chat = 'แชท';
  static const String notification = 'แจ้งเตือน';
  static const String settings = 'ตั้งค่า';
  
  // Screen titles
  static const String boardTitle = 'บอร์ดงาน';
  static const String chatTitle = 'แชท';
  static const String notificationTitle = 'การแจ้งเตือน';
  static const String settingsTitle = 'การตั้งค่า';

  // Error messages
  static const String networkError =
      'เกิดข้อผิดพลาดในการเชื่อมต่อเครือข่าย กรุณาตรวจสอบการเชื่อมต่อและลองใหม่อีกครั้ง';
  static const String invalidCredentials = 'อีเมลหรือรหัสผ่านไม่ถูกต้อง';
  static const String serverError =
      'เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ กรุณาลองใหม่อีกครั้งหรือติดต่อฝ่ายสนับสนุน';
  static const String unexpectedError =
      'เกิดข้อผิดพลาดที่ไม่คาดคิดระหว่างการเข้าสู่ระบบ';
  static const String logoutFailed = 'การออกจากระบบล้มเหลว';
}
